# BC Monitoring Service - Adhoc Tests

This directory contains comprehensive adhoc tests for the BC Monitoring Service based on the test matrix defined in `BC_Monitoring_Service_Test_Matrix.md`.

## Overview

The adhoc tests use TestContainers to set up local instances of:
- **LocalStack S3 service** for ABI file storage
- **DynamoDB service** for event and block height storage
- **WireMock** for mocking Besu blockchain components instead of using real instances

## Test Infrastructure

### TestContainers Configuration
- **AdhocTestHelper.groovy**: Sets up LocalStack containers with S3 and DynamoDB services
- **BlockchainMockHelper.groovy**: Sets up WireMock server to mock blockchain WebSocket and HTTP endpoints

### Test Structure
The tests are organized by functional areas matching the test matrix:

1. **service/ServiceInitializationSpec.groovy** - Service Initialization & Startup tests
2. **abi/AbiFileManagementSpec.groovy** - ABI File Management tests  
3. **monitoring/BlockchainEventMonitoringSpec.groovy** - Blockchain Event Monitoring tests

## Implemented Test Cases

### 1. Service Initialization & Startup
- **1.1.1 Successful Service Startup**: Verifies service starts with all dependencies available
- **1.1.2 Service Restart After WebSocket Error**: Tests automatic restart after WebSocket handshake errors

### 2. ABI File Management  
- **2.1.1 Successful ABI Download and Parsing**: Tests downloading and parsing valid ABI files from S3
- **2.1.2 Multiple Zone ABI Processing**: Tests processing ABI files from multiple zones (3000, 3001, etc.)
- **2.2.1 Non-JSON Files in S3 Bucket**: Tests skipping non-JSON files while processing JSON files normally
- **2.1.3 Hardhat vs Truffle ABI Format Handling**: Tests correct parsing based on ABI_FORMAT configuration

### 3. Blockchain Event Monitoring
- **3.1.1 New Block Event Detection**: Tests detection and processing of events from new blockchain blocks
- **3.1.3 Event Data Parsing and Storage**: Tests correct parsing of indexed and non-indexed event parameters
- **3.2.1 Block with No Matching Events**: Tests processing blocks without matching events
- **3.1.4 TraceId Extraction**: Tests extraction of traceId from event non-indexed values

## Running the Tests

### Prerequisites
- Docker must be running (for TestContainers)
- Java 21
- Gradle

### Execute Adhoc Tests
```bash
# Run all adhoc tests
./gradlew testAdhoc

# Run specific test class
./gradlew testAdhoc --tests "adhoc.service.ServiceInitializationSpec"

# Run with debug logging
./gradlew testAdhoc -Dlogging.level.adhoc=DEBUG
```

### Test Configuration
Tests use dynamic property configuration to override application settings:
- LocalStack endpoints are dynamically assigned
- WireMock server ports are configured automatically
- Test-specific timeouts and polling intervals are set

## Test Data Setup

### S3 Bucket Structure
```
abijson-local-bucket/
├── 3000/
│   ├── Account.json
│   ├── Token.json
│   └── Issuer.json
├── 3001/
│   ├── Account.json
│   └── Provider.json
└── 3002/
    └── Provider.json
```

### DynamoDB Tables
- **local-Events**: Stores blockchain events with transaction hash and log index as keys
- **local-BlockHeight**: Stores current block height with ID=1 as key

### Sample ABI Structure
```json
{
  "address": "0x993366A606A99129e56B4b99B27e428ba1Cb672f",
  "abi": [
    {
      "type": "event",
      "anonymous": false,
      "name": "AccountEnabled",
      "inputs": [
        {"type": "bytes32", "name": "accountId", "indexed": true},
        {"type": "bytes32", "name": "accountStatus", "indexed": false},
        {"type": "bytes32", "name": "reasonCode", "indexed": false},
        {"type": "bytes32", "name": "traceId", "indexed": false}
      ]
    }
  ]
}
```

## Mocking Strategy

### Blockchain Components
Instead of connecting to a real Besu blockchain:
- **WireMock** simulates WebSocket and HTTP endpoints
- Mock responses include realistic block data, transaction data, and event logs
- WebSocket handshake errors can be simulated for retry testing

### Mock Data Examples
- Block numbers: 0x1b4, 0x1b5, 0x1b6
- Transaction hashes: 32-byte hex strings
- Contract addresses: Match ABI file addresses
- Event topics: Include event signatures and indexed parameters

## Extending the Tests

### Adding New Test Cases
1. Create new test methods in existing spec files or new spec files
2. Follow the naming convention: `"X.Y.Z Test Name - should describe expected behavior"`
3. Use the test matrix reference numbers for traceability

### Adding New Mock Data
1. Extend `BlockchainMockHelper` for new blockchain scenarios
2. Extend `AdhocTestHelper` for new S3/DynamoDB data
3. Update WireMock stubs for new API responses

### Test Utilities
- **PollingConditions**: Used for eventually consistent assertions
- **Dynamic Properties**: Override configuration per test
- **TestContainers**: Manage external service dependencies

## Troubleshooting

### Common Issues
1. **Docker not running**: Ensure Docker is started before running tests
2. **Port conflicts**: TestContainers automatically assigns available ports
3. **Timeout issues**: Increase polling timeout for slow environments
4. **LocalStack startup**: Check Docker logs if LocalStack fails to start

### Debug Tips
- Enable debug logging: `-Dlogging.level.adhoc=DEBUG`
- Check TestContainer logs: Look for container startup messages
- Verify mock responses: Check WireMock admin interface
- Inspect DynamoDB data: Use AWS CLI with LocalStack endpoint

## Future Enhancements

### Additional Test Cases to Implement
- **1.3.1 Service Startup Failure - Invalid Environment Variables**
- **2.3.1 S3 Bucket Access Denied**
- **3.3.1 WebSocket Connection Failure**
- **4.1.1 Event Storage to DynamoDB**
- **5.1.1 Environment Variable Loading**
- **6.1.1 Retry Mechanism for WebSocket Errors**

### Infrastructure Improvements
- Add performance testing capabilities
- Implement chaos engineering scenarios
- Add metrics and monitoring validation
- Extend to support multiple blockchain networks

## References
- [BC_Monitoring_Service_Test_Matrix.md](../../../../BC_Monitoring_Service_Test_Matrix.md) - Complete test matrix
- [TestContainers Documentation](https://www.testcontainers.org/)
- [WireMock Documentation](http://wiremock.org/)
- [Spock Framework Documentation](https://spockframework.org/)
