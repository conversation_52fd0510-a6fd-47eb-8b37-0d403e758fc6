package adhoc.test.util

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.core.WireMockConfiguration
import org.web3j.protocol.core.methods.response.*
import org.web3j.protocol.core.methods.response.EthBlock.Block
import org.web3j.protocol.core.methods.response.EthBlock.TransactionResult
import org.web3j.protocol.core.methods.response.Transaction
import org.web3j.protocol.core.methods.response.Log
import java.math.BigInteger

/**
 * Helper class for mocking blockchain components using WireMock
 * instead of connecting to a real Besu blockchain instance.
 */
class BlockchainMockHelper {
    
    private static WireMockServer wireMockServer
    private static final int MOCK_PORT = 18541
    
    static {
        setupWireMockServer()
    }
    
    /**
     * Setup WireMock server to mock blockchain WebSocket and HTTP endpoints
     */
    private static void setupWireMockServer() {
        wireMockServer = new WireMockServer(
            WireMockConfiguration.options()
                .port(MOCK_PORT)
                .enableBrowserProxying(false)
        )
        
        wireMockServer.start()
        WireMock.configureFor("localhost", MOCK_PORT)
        
        setupDefaultMockResponses()
        println("Blockchain mock server started on port: ${MOCK_PORT}")
    }
    
    /**
     * Setup default mock responses for common blockchain operations
     */
    private static void setupDefaultMockResponses() {
        // Mock eth_blockNumber response
        WireMock.stubFor(
            WireMock.post(WireMock.urlEqualTo("/"))
                .withRequestBody(WireMock.containing("eth_blockNumber"))
                .willReturn(WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", "application/json")
                    .withBody('''
                        {
                            "jsonrpc": "2.0",
                            "id": 1,
                            "result": "0x1b4"
                        }
                    '''))
        )
        
        // Mock eth_getBlockByNumber response
        WireMock.stubFor(
            WireMock.post(WireMock.urlEqualTo("/"))
                .withRequestBody(WireMock.containing("eth_getBlockByNumber"))
                .willReturn(WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", "application/json")
                    .withBody(createMockBlockResponse()))
        )
        
        // Mock eth_getLogs response
        WireMock.stubFor(
            WireMock.post(WireMock.urlEqualTo("/"))
                .withRequestBody(WireMock.containing("eth_getLogs"))
                .willReturn(WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", "application/json")
                    .withBody(createMockLogsResponse()))
        )
        
        // Mock WebSocket subscription responses
        setupWebSocketMocks()
    }
    
    /**
     * Create mock block response with sample transaction data
     */
    private static String createMockBlockResponse() {
        return '''
        {
            "jsonrpc": "2.0",
            "id": 1,
            "result": {
                "number": "0x1b4",
                "hash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "parentHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
                "timestamp": "0x''' + Long.toHexString(System.currentTimeMillis() / 1000) + '''",
                "gasLimit": "0x1c9c380",
                "gasUsed": "0x5208",
                "transactions": [
                    {
                        "hash": "0xabc123def456789abc123def456789abc123def456789abc123def456789abc123",
                        "blockNumber": "0x1b4",
                        "blockHash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                        "transactionIndex": "0x0",
                        "from": "0x1234567890123456789012345678901234567890",
                        "to": "******************************************",
                        "value": "0x0",
                        "gas": "0x5208",
                        "gasPrice": "0x4a817c800",
                        "input": "0x"
                    }
                ],
                "logs": [
                    {
                        "address": "******************************************",
                        "topics": [
                            "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                            "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
                        ],
                        "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001",
                        "blockNumber": "0x1b4",
                        "transactionHash": "0xabc123def456789abc123def456789abc123def456789abc123def456789abc123",
                        "transactionIndex": "0x0",
                        "blockHash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                        "logIndex": "0x0"
                    }
                ]
            }
        }
        '''
    }
    
    /**
     * Create mock logs response for eth_getLogs
     */
    private static String createMockLogsResponse() {
        return '''
        {
            "jsonrpc": "2.0",
            "id": 1,
            "result": [
                {
                    "address": "******************************************",
                    "topics": [
                        "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                        "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
                    ],
                    "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001",
                    "blockNumber": "0x1b4",
                    "transactionHash": "0xabc123def456789abc123def456789abc123def456789abc123def456789abc123",
                    "transactionIndex": "0x0",
                    "blockHash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                    "logIndex": "0x0"
                }
            ]
        }
        '''
    }
    
    /**
     * Setup WebSocket mocks for blockchain event subscription
     */
    private static void setupWebSocketMocks() {
        // Mock WebSocket handshake
        WireMock.stubFor(
            WireMock.get(WireMock.urlEqualTo("/"))
                .withHeader("Upgrade", WireMock.equalTo("websocket"))
                .willReturn(WireMock.aResponse()
                    .withStatus(101)
                    .withHeader("Upgrade", "websocket")
                    .withHeader("Connection", "Upgrade"))
        )
        
        // Mock subscription responses
        WireMock.stubFor(
            WireMock.post(WireMock.urlEqualTo("/"))
                .withRequestBody(WireMock.containing("eth_subscribe"))
                .willReturn(WireMock.aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", "application/json")
                    .withBody('''
                        {
                            "jsonrpc": "2.0",
                            "id": 1,
                            "result": "0x1234567890abcdef"
                        }
                    '''))
        )
    }
    
    /**
     * Create a mock AccountEnabled event log
     */
    static Map<String, Object> createMockAccountEnabledEvent() {
        return [
            address: "******************************************",
            topics: [
                "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", // Event signature
                "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"  // accountId (indexed)
            ],
            data: "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001",
            blockNumber: "0x1b4",
            transactionHash: "0xabc123def456789abc123def456789abc123def456789abc123def456789abc123",
            transactionIndex: "0x0",
            blockHash: "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
            logIndex: "0x0"
        ]
    }
    
    /**
     * Simulate WebSocket handshake error for retry testing
     */
    static void simulateWebSocketHandshakeError() {
        WireMock.stubFor(
            WireMock.get(WireMock.urlEqualTo("/"))
                .withHeader("Upgrade", WireMock.equalTo("websocket"))
                .willReturn(WireMock.aResponse()
                    .withStatus(400)
                    .withBody("WebSocket handshake error"))
        )
    }
    
    /**
     * Reset WebSocket mocks to normal operation
     */
    static void resetWebSocketMocks() {
        WireMock.reset()
        setupDefaultMockResponses()
    }
    
    // Getters
    static WireMockServer getWireMockServer() { return wireMockServer }
    static int getMockPort() { return MOCK_PORT }
    static String getMockEndpoint() { return "http://localhost:${MOCK_PORT}" }
    static String getMockWebSocketEndpoint() { return "ws://localhost:${MOCK_PORT}" }
    
    /**
     * Clean up resources
     */
    static void cleanup() {
        if (wireMockServer != null) {
            wireMockServer.stop()
        }
    }
}
