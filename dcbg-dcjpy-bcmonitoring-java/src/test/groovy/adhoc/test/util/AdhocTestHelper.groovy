package adhoc.test.util

import com.fasterxml.jackson.databind.ObjectMapper
import org.testcontainers.containers.localstack.LocalStackContainer
import org.testcontainers.utility.DockerImageName
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.core.sync.RequestBody

import java.nio.file.Files
import java.nio.file.Paths

/**
 * Helper class for adhoc tests providing TestContainers setup and utilities
 * for LocalStack S3 and DynamoDB services.
 */
class AdhocTestHelper {
    
    private static final String LOCALSTACK_IMAGE = "localstack/localstack:3.0.2"
    private static final String ACCESS_KEY = "test"
    private static final String SECRET_KEY = "test"
    private static final Region REGION = Region.AP_NORTHEAST_1
    
    private static LocalStackContainer localstack
    private static S3Client s3Client
    private static DynamoDbClient dynamoDbClient
    private static ObjectMapper objectMapper = new ObjectMapper()
    
    static {
        startLocalStack()
        setupAwsClients()
        createDynamoDBTables()
        setupS3Bucket()
    }
    
    /**
     * Start LocalStack container with S3 and DynamoDB services
     */
    private static void startLocalStack() {
        localstack = new LocalStackContainer(DockerImageName.parse(LOCALSTACK_IMAGE))
                .withServices(LocalStackContainer.Service.S3, LocalStackContainer.Service.DYNAMODB)
                .withEnv("DEFAULT_REGION", REGION.id())
                .withEnv("SKIP_SSL_CERT_DOWNLOAD", "true")
        
        localstack.start()
        
        println("LocalStack started on endpoint: ${localstack.getEndpointOverride(LocalStackContainer.Service.S3)}")
    }
    
    /**
     * Setup AWS clients for S3 and DynamoDB
     */
    private static void setupAwsClients() {
        def credentials = StaticCredentialsProvider.create(
                AwsBasicCredentials.create(ACCESS_KEY, SECRET_KEY)
        )
        
        s3Client = S3Client.builder()
                .endpointOverride(localstack.getEndpointOverride(LocalStackContainer.Service.S3))
                .credentialsProvider(credentials)
                .region(REGION)
                .forcePathStyle(true)
                .build()
        
        dynamoDbClient = DynamoDbClient.builder()
                .endpointOverride(localstack.getEndpointOverride(LocalStackContainer.Service.DYNAMODB))
                .credentialsProvider(credentials)
                .region(REGION)
                .build()
    }
    
    /**
     * Create required DynamoDB tables for testing
     */
    private static void createDynamoDBTables() {
        // Create Events table
        createEventsTable()
        
        // Create BlockHeight table
        createBlockHeightTable()
    }
    
    /**
     * Create Events table in DynamoDB
     */
    private static void createEventsTable() {
        def createTableRequest = CreateTableRequest.builder()
                .tableName("local-Events")
                .keySchema(
                        KeySchemaElement.builder()
                                .attributeName("transactionHash")
                                .keyType(KeyType.HASH)
                                .build(),
                        KeySchemaElement.builder()
                                .attributeName("logIndex")
                                .keyType(KeyType.RANGE)
                                .build()
                )
                .attributeDefinitions(
                        AttributeDefinition.builder()
                                .attributeName("transactionHash")
                                .attributeType(ScalarAttributeType.S)
                                .build(),
                        AttributeDefinition.builder()
                                .attributeName("logIndex")
                                .attributeType(ScalarAttributeType.N)
                                .build()
                )
                .provisionedThroughput(
                        ProvisionedThroughput.builder()
                                .readCapacityUnits(5L)
                                .writeCapacityUnits(5L)
                                .build()
                )
                .build()
        
        dynamoDbClient.createTable(createTableRequest)
        println("Created Events table")
    }
    
    /**
     * Create BlockHeight table in DynamoDB
     */
    private static void createBlockHeightTable() {
        def createTableRequest = CreateTableRequest.builder()
                .tableName("local-BlockHeight")
                .keySchema(
                        KeySchemaElement.builder()
                                .attributeName("id")
                                .keyType(KeyType.HASH)
                                .build()
                )
                .attributeDefinitions(
                        AttributeDefinition.builder()
                                .attributeName("id")
                                .attributeType(ScalarAttributeType.N)
                                .build()
                )
                .provisionedThroughput(
                        ProvisionedThroughput.builder()
                                .readCapacityUnits(5L)
                                .writeCapacityUnits(5L)
                                .build()
                )
                .build()
        
        dynamoDbClient.createTable(createTableRequest)
        println("Created BlockHeight table")
    }
    
    /**
     * Setup S3 bucket and upload sample ABI files
     */
    private static void setupS3Bucket() {
        def bucketName = "abijson-local-bucket"
        
        // Create bucket
        s3Client.createBucket(CreateBucketRequest.builder().bucket(bucketName).build())
        println("Created S3 bucket: ${bucketName}")
        
        // Upload sample ABI files
        uploadSampleAbiFiles(bucketName)
    }
    
    /**
     * Upload sample ABI files to S3 bucket
     */
    private static void uploadSampleAbiFiles(String bucketName) {
        // Sample ABI content for Account contract
        def accountAbi = [
            address: "0x993366A606A99129e56B4b99B27e428ba1Cb672f",
            abi: [
                [
                    type: "event",
                    anonymous: false,
                    name: "AccountEnabled",
                    inputs: [
                        [type: "bytes32", name: "accountId", indexed: true],
                        [type: "bytes32", name: "accountStatus", indexed: false],
                        [type: "bytes32", name: "reasonCode", indexed: false],
                        [type: "bytes32", name: "traceId", indexed: false]
                    ]
                ]
            ]
        ]
        
        // Upload to zone 3000
        uploadAbiFile(bucketName, "3000/Account.json", accountAbi)
        
        // Upload to zone 3001
        uploadAbiFile(bucketName, "3001/Account.json", accountAbi)
        
        println("Uploaded sample ABI files")
    }
    
    /**
     * Upload ABI file to S3
     */
    private static void uploadAbiFile(String bucketName, String key, Object abiContent) {
        def jsonContent = objectMapper.writeValueAsString(abiContent)
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(key)
                        .build(),
                RequestBody.fromString(jsonContent)
        )
    }
    
    // Getters for test access
    static LocalStackContainer getLocalStack() { return localstack }
    static S3Client getS3Client() { return s3Client }
    static DynamoDbClient getDynamoDbClient() { return dynamoDbClient }
    static String getLocalStackEndpoint() { 
        return localstack.getEndpointOverride(LocalStackContainer.Service.S3).toString() 
    }
    static String getAccessKey() { return ACCESS_KEY }
    static String getSecretKey() { return SECRET_KEY }
    static String getRegion() { return REGION.id() }
    
    /**
     * Clean up resources
     */
    static void cleanup() {
        if (s3Client != null) s3Client.close()
        if (dynamoDbClient != null) dynamoDbClient.close()
        if (localstack != null) localstack.stop()
    }
}
