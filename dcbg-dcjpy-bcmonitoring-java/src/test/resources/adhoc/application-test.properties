# Test configuration for BC Monitoring Service Adhoc Tests
# This configuration is used by the adhoc test cases to override default settings

# Spring Boot Test Configuration
spring.application.name=BcmonitoringTest
server.port=0

# Logging Configuration for Tests
logging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG
logging.level.adhoc=DEBUG
logging.level.org.testcontainers=INFO
logging.level.com.github.tomakehurst.wiremock=WARN

# AWS Configuration (will be overridden by test dynamic properties)
aws.region=ap-northeast-1
aws.access-key-id=test
aws.secret-access-key=test

# DynamoDB Configuration (will be overridden by test dynamic properties)
aws.dynamodb.region=ap-northeast-1
aws.dynamodb.table-prefix=test
aws.dynamodb.endpoint=http://localhost:4566
aws.dynamodb.events-table-name=test-Events
aws.dynamodb.block-height-table-name=test-BlockHeight

# S3 Configuration (will be overridden by test dynamic properties)
aws.s3.bucket-name=test-bucket
aws.s3.region=ap-northeast-1

# LocalStack Configuration (will be overridden by test dynamic properties)
local-stack.end-point=http://localhost:4566
local-stack.region=ap-northeast-1
local-stack.access-key=test
local-stack.secret-key=test

# Blockchain Configuration (will be overridden by test dynamic properties)
websocket.uri.host=localhost
websocket.uri.port=18541
ethereum.endpoint=http://localhost:18541

# Environment Configuration
env=local
abi-format=hardhat

# Subscription Configuration
subscription.check-interval=1000
subscription.allowable-block-timestamp-diff-sec=2

# Test-specific Configuration
test.timeout.seconds=30
test.polling.initial-delay=1
test.polling.factor=1.25

# Disable actual monitoring during tests
monitoring.enabled=false
monitoring.auto-start=false
