#!/bin/bash

# BC Monitoring Service - Adhoc Test Runner
# This script provides convenient commands to run the adhoc tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if Java is available
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH."
        exit 1
    fi
    
    # Check Java version
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -lt 21 ]; then
        print_error "Java 21 or higher is required. Current version: $java_version"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to run all adhoc tests
run_all_tests() {
    print_status "Running all adhoc tests..."
    ./gradlew testAdhoc
    print_success "All adhoc tests completed"
}

# Function to run specific test category
run_service_tests() {
    print_status "Running Service Initialization tests..."
    ./gradlew testAdhoc --tests "adhoc.service.*"
}

run_abi_tests() {
    print_status "Running ABI File Management tests..."
    ./gradlew testAdhoc --tests "adhoc.abi.*"
}

run_monitoring_tests() {
    print_status "Running Blockchain Event Monitoring tests..."
    ./gradlew testAdhoc --tests "adhoc.monitoring.*"
}

# Function to run specific test class
run_specific_test() {
    local test_class=$1
    print_status "Running specific test: $test_class"
    ./gradlew testAdhoc --tests "$test_class"
}

# Function to run tests with debug logging
run_tests_debug() {
    print_status "Running adhoc tests with debug logging..."
    ./gradlew testAdhoc -Dlogging.level.adhoc=DEBUG -Dlogging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG
}

# Function to clean and run tests
clean_and_test() {
    print_status "Cleaning and running adhoc tests..."
    ./gradlew clean testAdhoc
}

# Function to show test report
show_test_report() {
    local report_path="build/reports/tests/testAdhoc/index.html"
    if [ -f "$report_path" ]; then
        print_status "Opening test report..."
        if command -v open &> /dev/null; then
            open "$report_path"  # macOS
        elif command -v xdg-open &> /dev/null; then
            xdg-open "$report_path"  # Linux
        else
            print_status "Test report available at: $report_path"
        fi
    else
        print_warning "Test report not found. Run tests first."
    fi
}

# Function to show usage
show_usage() {
    echo "BC Monitoring Service - Adhoc Test Runner"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  all                    Run all adhoc tests"
    echo "  service               Run Service Initialization tests"
    echo "  abi                   Run ABI File Management tests"
    echo "  monitoring            Run Blockchain Event Monitoring tests"
    echo "  debug                 Run all tests with debug logging"
    echo "  clean                 Clean and run all tests"
    echo "  report                Show test report in browser"
    echo "  specific <class>      Run specific test class"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all"
    echo "  $0 service"
    echo "  $0 specific adhoc.service.ServiceInitializationSpec"
    echo "  $0 debug"
    echo ""
    echo "Test Categories:"
    echo "  - Service Initialization & Startup"
    echo "  - ABI File Management"
    echo "  - Blockchain Event Monitoring"
    echo ""
    echo "Prerequisites:"
    echo "  - Docker must be running"
    echo "  - Java 21 or higher"
    echo "  - Gradle (wrapper included)"
}

# Main script logic
main() {
    case "${1:-help}" in
        "all")
            check_prerequisites
            run_all_tests
            ;;
        "service")
            check_prerequisites
            run_service_tests
            ;;
        "abi")
            check_prerequisites
            run_abi_tests
            ;;
        "monitoring")
            check_prerequisites
            run_monitoring_tests
            ;;
        "debug")
            check_prerequisites
            run_tests_debug
            ;;
        "clean")
            check_prerequisites
            clean_and_test
            ;;
        "report")
            show_test_report
            ;;
        "specific")
            if [ -z "$2" ]; then
                print_error "Please specify a test class name"
                echo "Example: $0 specific adhoc.service.ServiceInitializationSpec"
                exit 1
            fi
            check_prerequisites
            run_specific_test "$2"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
